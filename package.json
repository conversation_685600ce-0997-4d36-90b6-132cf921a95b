{"name": "chronicles-uae", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:memory": "cross-env NODE_OPTIONS=--max-old-space-size=8192 next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.11", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/postcss": "^4.1.11", "@tiptap/extension-bubble-menu": "^2.22.3", "@tiptap/extension-bullet-list": "^2.23.0", "@tiptap/extension-color": "^2.22.3", "@tiptap/extension-floating-menu": "^2.22.3", "@tiptap/extension-font-family": "^2.22.3", "@tiptap/extension-image": "^2.22.3", "@tiptap/extension-link": "^2.22.3", "@tiptap/extension-list-item": "^2.23.0", "@tiptap/extension-ordered-list": "^2.23.0", "@tiptap/extension-placeholder": "^2.22.3", "@tiptap/extension-table": "^2.22.3", "@tiptap/extension-table-cell": "^2.22.3", "@tiptap/extension-table-header": "^2.22.3", "@tiptap/extension-table-row": "^2.22.3", "@tiptap/extension-text-align": "^2.22.3", "@tiptap/extension-text-style": "^2.22.3", "@tiptap/pm": "^2.22.3", "@tiptap/react": "^2.22.3", "@tiptap/starter-kit": "^2.22.3", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.2.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.0", "gsap": "^3.13.0", "lucide-react": "^0.510.0", "mongoose": "^8.14.3", "next": "15.3.2", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-intersection-observer": "^9.16.0", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/node-fetch": "^2.6.12", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.3.2", "eslint-plugin-unicorn": "^59.0.1", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.2.9", "typescript": "^5"}}