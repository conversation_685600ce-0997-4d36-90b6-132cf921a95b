"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { ChevronDown } from "lucide-react";
import { supabase } from "@/lib/supabase";
import { AboutHeroSectionData } from "@/types/about";

const AboutHero = () => {
    const [heroData, setHeroData] = useState<AboutHeroSectionData>({
        id: "",
        hero_heading: "About Us",
        hero_subheading:
            "Discover the story behind our passion for creating unforgettable exhibition experiences.",
        background_image_id: undefined,
        background_image_url: undefined,
        background_image_alt: undefined,
        fallback_image_url:
            "https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
        overlay_opacity: 0.3,
        overlay_color: "black",
        text_color: "white",
        height_class: "h-[60vh]",
        show_scroll_indicator: true,
        is_active: true,
        created_at: "",
        updated_at: "",
    });

    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        loadAboutHeroData();
    }, []);

    const loadAboutHeroData = async () => {
        try {
            setLoading(true);
            setError(null);

            // Use the database function to get about hero data
            const { data, error } = await supabase.rpc(
                "get_about_hero_section",
            );

            console.log(data);

            if (error) {
                console.error("Error fetching about hero data:", error);
                setError("Failed to load hero section data");
                return;
            }

            if (data && data.length > 0) {
                const heroSection = data[0];

                // Construct proper image URL if file path exists
                let imageUrl = null;
                if (heroSection.background_image_url) {
                    // Get the public URL from Supabase storage
                    const { data: urlData } = supabase.storage
                        .from("about-hero")
                        .getPublicUrl(heroSection.background_image_url);
                    imageUrl = urlData.publicUrl;
                }

                setHeroData({
                    ...heroSection,
                    background_image_url: imageUrl,
                });
            } else {
                // Fallback to default data if no data found
                setHeroData({
                    id: "",
                    hero_heading: "About Us",
                    hero_subheading:
                        "Discover the story behind our passion for creating unforgettable exhibition experiences.",
                    background_image_id: undefined,
                    background_image_url: undefined,
                    background_image_alt: undefined,
                    fallback_image_url:
                        "https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
                    overlay_opacity: 0.3,
                    overlay_color: "black",
                    text_color: "white",
                    height_class: "h-[75vh]",
                    show_scroll_indicator: true,
                    is_active: true,
                    created_at: "",
                    updated_at: "",
                });
            }
        } catch (error) {
            console.error("Error loading about hero data:", error);
            setError("Failed to load hero section data");
            // Fallback to default data on error
            setHeroData({
                id: "",
                hero_heading: "About Us",
                hero_subheading:
                    "Discover the story behind our passion for creating unforgettable exhibition experiences.",
                background_image_id: undefined,
                background_image_url: undefined,
                background_image_alt: undefined,
                fallback_image_url:
                    "https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
                overlay_opacity: 0.3,
                overlay_color: "black",
                text_color: "white",
                height_class: "h-[60vh]",
                show_scroll_indicator: true,
                is_active: true,
                created_at: "",
                updated_at: "",
            });
        } finally {
            setLoading(false);
        }
    };

    // Show loading state
    if (loading) {
        return (
            <section className="relative h-[60vh] flex items-center justify-center overflow-hidden">
                <div className="absolute inset-0 bg-gray-200 animate-pulse" />
                <div className="relative z-10 text-center w-full px-4 sm:px-6 md:px-8 lg:px-12">
                    <div className="animate-pulse">
                        <div className="h-12 bg-gray-300 rounded mb-6 mx-auto max-w-md"></div>
                        <div className="h-6 bg-gray-300 rounded mx-auto max-w-2xl"></div>
                    </div>
                </div>
            </section>
        );
    }

    // Don't render if section is not active
    if (!heroData.is_active) {
        return null;
    }

    // Determine background image URL
    const backgroundImageUrl =
        heroData.background_image_url || heroData.fallback_image_url;

    return (
        <section
            className={`hero relative h-[75vh] 2xl:h-[60vh] flex items-center justify-center overflow-hidden`}
        >
            {/* Background Image */}
            <div
                className="absolute inset-0 bg-cover bg-center bg-no-repeat"
                style={{
                    backgroundImage: `url('${backgroundImageUrl}')`,
                }}
            />

            {/* Dynamic Overlay */}
            <div
                className="absolute inset-0"
                style={{
                    backgroundColor: heroData.overlay_color,
                    opacity: heroData.overlay_opacity,
                }}
            />

            {/* Content */}
            <div
                className="relative z-10 text-center w-full mt-16 px-4 sm:px-6 md:px-8 lg:px-12"
                style={{ color: heroData.text_color }}
            >
                <motion.h1
                    className="text-3xl sm:text-4xl md:text-5xl font-rubik font-bold mb-4 sm:mb-6 leading-tight"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.2 }}
                >
                    {heroData.hero_heading}
                </motion.h1>

                <motion.h3
                    className="text-lg sm:text-2xl font-markazi-text font-medium max-w-xs sm:max-w-md md:max-w-2xl lg:max-w-3xl mx-auto leading-relaxed tracking-wide opacity-90"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.4 }}
                >
                    {heroData.hero_subheading}
                </motion.h3>
            </div>

            {/* Scroll Down Indicator */}
            {heroData.show_scroll_indicator && (
                <motion.div
                    className="absolute bottom-4 left-1/2 transform -translate-x-1/2 cursor-pointer"
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.6 }}
                    whileHover={{ y: 5 }}
                >
                    <ChevronDown
                        className="w-8 h-8 animate-bounce"
                        style={{ color: heroData.text_color }}
                    />
                </motion.div>
            )}
        </section>
    );
};

export default AboutHero;
